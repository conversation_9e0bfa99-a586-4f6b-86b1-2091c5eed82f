import { useCallback, useRef, useEffect, useState } from 'react';

/**
 * Custom hook for debouncing function calls to prevent excessive re-renders and API calls
 * @param {Function} callback - The function to debounce
 * @param {number} delay - The delay in milliseconds
 * @returns {Function} - The debounced function
 */
export const useDebounce = (callback, delay = 300) => {
  const timeoutRef = useRef(null);
  const callbackRef = useRef(callback);

  // Update callback ref when callback changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return useCallback((...args) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      callbackRef.current(...args);
    }, delay);
  }, [delay]);
};

/**
 * Custom hook for debouncing state updates
 * @param {*} value - The value to debounce
 * @param {number} delay - The delay in milliseconds
 * @returns {*} - The debounced value
 */
export const useDebouncedValue = (value, delay = 300) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Custom hook for stable state updates that prevent unnecessary re-renders
 * @param {*} initialValue - The initial state value
 * @returns {Array} - [state, stableSetState]
 */
export const useStableState = (initialValue) => {
  const [state, setState] = useState(initialValue);
  const stateRef = useRef(state);

  const stableSetState = useCallback((newValue) => {
    const value = typeof newValue === 'function' ? newValue(stateRef.current) : newValue;
    
    // Only update if the value actually changed
    if (value !== stateRef.current) {
      stateRef.current = value;
      setState(value);
    }
  }, []);

  // Keep ref in sync
  useEffect(() => {
    stateRef.current = state;
  }, [state]);

  return [state, stableSetState];
};
