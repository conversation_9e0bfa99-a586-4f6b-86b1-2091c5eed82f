"use client";
import React, { useState, useEffect, useMemo, useRef, useCallback } from "react";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import "@/styles/tabs/codeGenerationPanel/CodeViewPanel.css";


// Loading spinner icon component
const LoadingIcon = () => (
  <svg className="animate-pulse" width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_204_169332)">
      <g filter="url(#filter0_d_204_169332)">
        <path d="M19.6079 14.7872C17.6453 13.998 15.7347 13.0931 13.864 12.1115C13.7202 12.0351 13.577 11.9574 13.4343 11.879C15.4316 10.7872 17.4977 9.82389 19.6079 8.96641C20.5449 8.61552 20.8802 7.38299 20.2397 6.60873C19.7171 5.95481 18.7634 5.84831 18.1095 6.37092C16.4448 7.67591 14.7059 8.87814 12.9203 10.0075C12.7821 10.094 12.6434 10.179 12.5039 10.2631C12.5571 7.9875 12.7558 5.71656 13.0683 3.46033C13.2329 2.47351 12.3333 1.56685 11.3423 1.73439C10.5433 1.8556 9.98551 2.58023 10.061 3.37469C10.6267 5.88366 10.9512 8.47125 11.0035 11.117C11.0057 11.2253 11.0077 11.3335 11.009 11.4418C11.0103 11.565 11.0114 11.6884 11.0116 11.8118C11.0116 11.8333 11.0121 11.8548 11.0121 11.8766C11.0121 11.8965 11.0118 11.9165 11.0116 11.9367C11.0116 11.9383 11.0116 11.9398 11.0116 11.9413C11.0116 12.0738 11.0103 12.2057 11.0088 12.3377C11.0086 12.3568 11.0081 12.3757 11.0079 12.3946C11.0072 12.4427 11.0066 12.4907 11.0057 12.5388C10.9598 15.2182 10.6335 17.8385 10.061 20.3782C9.98551 21.1729 10.5435 21.8973 11.3423 22.0185C12.3331 22.1861 13.2327 21.2796 13.0683 20.2926C12.756 18.0379 12.5573 15.7683 12.5039 13.4942C14.4466 14.6774 16.3128 15.9841 18.1093 17.382C18.8815 18.0179 20.1167 17.6923 20.4669 16.7502C20.7719 15.9707 20.3872 15.0915 19.6077 14.7867L19.6079 14.7872Z" fill="hsl(var(--primary))" />
        <path d="M6.70309 13.4001C6.44266 13.5221 6.1807 13.6418 5.91786 13.7599C5.12779 14.1146 4.33706 14.467 3.53163 14.7872C2.59817 15.1392 2.26419 16.3666 2.9012 17.1402C3.42228 17.795 4.37527 17.9035 5.03007 17.3824C5.25691 17.2019 5.48681 17.0269 5.71737 16.8526C5.94882 16.6802 6.17982 16.5067 6.41324 16.3377C6.8781 15.9971 7.34494 15.6591 7.81726 15.3295C8.91211 14.5237 7.95977 12.8537 6.70309 13.3998V13.4001Z" fill="hsl(var(--primary))" />
        <path d="M7.8173 8.4238C7.58125 8.25933 7.34673 8.09223 7.11287 7.9238C6.41086 7.41678 5.71017 6.90845 5.03011 6.3709C4.25849 5.7385 3.02838 6.06304 2.67704 7.00133C2.3705 7.77998 2.75302 8.65963 3.53167 8.96617C3.80132 9.07245 4.06789 9.184 4.33403 9.29643C4.59907 9.41061 4.86477 9.52392 5.12783 9.64162C5.65527 9.87394 6.18139 10.1093 6.70291 10.3535C7.94817 10.8987 8.9183 9.2389 7.81708 8.4238H7.8173Z" fill="hsl(var(--primary))" />
      </g>
    </g>
    <defs>
      <filter id="filter0_d_204_169332" x="-3" y="-1" width="30" height="30" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
        <feOffset dy="2" />
        <feGaussianBlur stdDeviation="1.5" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.13 0" />
        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_204_169332" />
        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_204_169332" result="shape" />
      </filter>
      <clipPath id="clip0_204_169332">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);


const LoadingView = ({ message = "Spinning up Editor" }) => {
  const [hint, setHint] = useState('');

  // Predefined hints that appear randomly
  const hints = useMemo(() => [
    "Optimizing your workspace...",
    "Loading editor tools...",
    "Preparing your environment...",
    "Connecting to resources...",
    "Initializing components...",
    "Setting up your preferences..."
  ], []);

  useEffect(() => {
    // Change hint text periodically
    const hintInterval = setInterval(() => {
      const randomIndex = Math.floor(Math.random() * hints.length);
      setHint(hints[randomIndex]);
    }, 3000);

    // Set initial hint
    setHint(hints[Math.floor(Math.random() * hints.length)]);

    return () => {
      clearInterval(hintInterval);
    };
  }, [hints]);

  return (
    <div className="flex flex-col items-center h-full w-full bg-white text-gray-800 pt-16">
      <div className="flex flex-col items-center h-full w-full max-w-lg mx-auto">
        {/* Top area with background ellipse */}
        <div className="relative w-32 h-32 mb-6">
          {/* Background ellipse with pulse effect */}
          <div className="absolute inset-0 bg-primary-50 rounded-full animate-pulse opacity-50"></div>

          {/* Loader icon */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-24 h-24 flex items-center justify-center">
              <LoadingIcon />
            </div>
          </div>
        </div>

        {/* Status text */}
        <div className="mb-8 text-center">
          <h2 className="text-gray-800 font-weight-semibold typography-heading-2 mb-1">
            {message}
          </h2>
          <p className="text-gray-500 typography-body-sm">
            {hint || "Setting up your environment"}
          </p>
        </div>
      </div>
    </div>
  );
};

const CodeComponent = () => {
  const { currentIframeUrl, isReady, codeEditorKey } = useCodeGeneration();

  // Enhanced state management for stable iframe behavior
  const [iframeState, setIframeState] = useState({
    isLoaded: false,
    isLoading: false,
    error: null,
    hasLoadedSuccessfully: false, // Track if iframe has ever loaded successfully
    isRefreshing: false // Track intentional refreshes
  });

  const [showRefreshLoader, setShowRefreshLoader] = useState(false);

  // Refs for tracking previous states and preventing cascading updates
  const iframeRef = useRef(null);
  const debounceTimerRef = useRef(null);
  const previousStatesRef = useRef({
    isReady: false,
    codeEditorKey: codeEditorKey,
    currentIframeUrl: null,
    hasInitialLoad: false
  });

  // Debounce ref to prevent rapid successive operations
  const debounceTimeoutRef = useRef(null);

  // Validate URL helper function
  const isValidUrl = useCallback((url) => {
    if (!url || typeof url !== 'string') return false;
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }, []);

  // Determine if we have a valid URL
  const hasValidUrl = currentIframeUrl && isValidUrl(currentIframeUrl);

  // Helper function to safely update iframe state with debouncing
  const updateIframeState = useCallback((updates) => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      setIframeState(prev => ({ ...prev, ...updates }));
    }, 50); // 50ms debounce
  }, []);

  // Helper function to check if refresh should be allowed
  const shouldAllowRefresh = useCallback(() => {
    return iframeState.hasLoadedSuccessfully &&
           !iframeState.isLoading &&
           !iframeState.isRefreshing;
  }, [iframeState.hasLoadedSuccessfully, iframeState.isLoading, iframeState.isRefreshing]);

  // Handle iframe load success - mark as successfully loaded and prevent auto-refreshes
  const handleIframeLoad = useCallback(() => {
    updateIframeState({
      isLoaded: true,
      isLoading: false,
      error: null,
      hasLoadedSuccessfully: true,
      isRefreshing: false
    });
  }, [updateIframeState]);

  // Handle iframe load error
  const handleIframeError = useCallback(() => {
    updateIframeState({
      isLoaded: false,
      isLoading: false,
      error: 'The code editor cannot be embedded due to security restrictions.',
      isRefreshing: false
    });
  }, [updateIframeState]);



  // Initial loading detection - only runs for first load or intentional refreshes
  useEffect(() => {
    // Only start loading if we have a valid URL and we're not already loaded successfully
    if (hasValidUrl && !iframeState.hasLoadedSuccessfully && !iframeState.isLoading && !iframeState.error) {
      // Mark as loading to prevent other effects from interfering
      updateIframeState({
        isLoading: true,
        isLoaded: false,
        error: null
      });

      // For VS Code and similar complex applications, onLoad often doesn't fire
      // Use a timeout approach as fallback
      const loadTimer = setTimeout(() => {
        // Only update if we're still in loading state (not interrupted)
        setIframeState(current => {
          if (current.isLoading && !current.hasLoadedSuccessfully) {
            return {
              ...current,
              isLoaded: true,
              isLoading: false,
              hasLoadedSuccessfully: true,
              isRefreshing: false
            };
          }
          return current;
        });
      }, 8000); // 8 seconds to allow full loading

      return () => {
        clearTimeout(loadTimer);
      };
    }
  }, [hasValidUrl, iframeState.hasLoadedSuccessfully, iframeState.isLoading, iframeState.error, updateIframeState]);

  // Handle intentional refresh (from modal refresh button) - ONLY source of refreshes
  useEffect(() => {
    const prevStates = previousStatesRef.current;
    const hasKeyChanged = prevStates.codeEditorKey !== codeEditorKey;

    // Only refresh if:
    // 1. Key actually changed (intentional refresh)
    // 2. We have a valid URL
    // 3. Iframe has loaded successfully before
    // 4. We're not currently loading or refreshing
    if (hasKeyChanged &&
        hasValidUrl &&
        shouldAllowRefresh()) {

      // Mark as intentional refresh
      updateIframeState({
        isRefreshing: true,
        isLoading: true,
        isLoaded: false,
        error: null
      });

      setShowRefreshLoader(true);

      // Hide refresh loader after timeout
      setTimeout(() => {
        setShowRefreshLoader(false);
      }, 2000);
    }

    // Update the ref to current key
    previousStatesRef.current = {
      ...prevStates,
      codeEditorKey: codeEditorKey
    };
  }, [codeEditorKey, hasValidUrl, shouldAllowRefresh, updateIframeState]);

  // Track isReady state transitions - NO AUTO-REFRESH, just tracking
  useEffect(() => {
    const prevStates = previousStatesRef.current;

    // Only track the state change if it actually changed to prevent unnecessary updates
    if (prevStates.isReady !== isReady) {
      previousStatesRef.current = {
        ...prevStates,
        isReady: isReady
      };
    }
  }, [isReady]);

  // Handle URL changes - only reset when URL actually changes to a different valid URL
  useEffect(() => {
    const prevStates = previousStatesRef.current;
    const hasUrlChanged = prevStates.currentIframeUrl !== currentIframeUrl;

    if (hasUrlChanged) {
      if (currentIframeUrl && isValidUrl(currentIframeUrl)) {
        // Only reset if we haven't loaded this URL successfully before
        // This prevents unnecessary resets when the same URL is set multiple times
        if (prevStates.currentIframeUrl !== currentIframeUrl) {
          updateIframeState({
            isLoaded: false,
            isLoading: false,
            error: null,
            hasLoadedSuccessfully: false,
            isRefreshing: false
          });
        }
      } else if (currentIframeUrl && !isValidUrl(currentIframeUrl)) {
        updateIframeState({
          isLoaded: false,
          isLoading: false,
          error: 'Invalid editor URL provided',
          hasLoadedSuccessfully: false,
          isRefreshing: false
        });
      }

      // Update the ref
      previousStatesRef.current = {
        ...prevStates,
        currentIframeUrl: currentIframeUrl
      };
    }
  }, [currentIframeUrl, isValidUrl, updateIframeState]);

  // Cleanup effect to prevent memory leaks
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // Render loading state
  const renderLoadingState = () => (
    <div className="code-loading-overlay code-fade-in">
      <LoadingView message={showRefreshLoader ? "Refreshing Editor..." : "Spinning up Editor"} />
    </div>
  );

  // Render error state
  const renderErrorState = () => (
    <div className="code-error-state code-fade-in">
      <div className="text-center max-w-md p-8">
        <div className="w-16 h-16 mx-auto mb-4 text-red-500">
          <svg fill="currentColor" viewBox="0 0 20 20" className="w-full h-full">
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
              clipRule="evenodd"
            />
          </svg>
        </div>
        <h3 className="text-lg font-semibold mb-2 text-gray-800">Editor Load Error</h3>
        <p className="text-gray-600 mb-4">{iframeState.error}</p>

        <div className="flex flex-col gap-3">
          <button
            onClick={() => window.open(currentIframeUrl, '_blank')}
            className="inline-flex items-center justify-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-600 transition-colors duration-200"
            disabled={!currentIframeUrl}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
            Open in New Tab
          </button>

          <p className="text-sm text-gray-500">
            You can also try using the refresh button in the toolbar.
          </p>
        </div>
      </div>
    </div>
  );

  // Determine what to show based on new state structure
  const shouldShowLoading = !hasValidUrl || iframeState.isLoading || (!iframeState.isLoaded && !iframeState.error);
  const shouldShowIframe = hasValidUrl && !iframeState.error;

  return (
    <div className="code-container">
      {/* Error state - highest priority */}
      {iframeState.error && renderErrorState()}

      {/* Loading state - show when loading or no valid URL */}
      {shouldShowLoading && !iframeState.error && renderLoadingState()}

      {/* Main iframe - render when we have valid URL and no error */}
      {shouldShowIframe && (
        <iframe
          key={iframeState.isRefreshing ? `refresh-${codeEditorKey}` : `stable-${currentIframeUrl}`}
          ref={iframeRef}
          src={currentIframeUrl}
          className={`code-iframe ${!iframeState.isLoaded ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
          sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-downloads allow-modals allow-orientation-lock allow-pointer-lock allow-presentation allow-top-navigation-by-user-activation"
          title="Code Server"
          allow="clipboard-read *; clipboard-write *; fullscreen; camera; microphone"
          onLoad={handleIframeLoad}
          onError={handleIframeError}
          referrerPolicy="no-referrer-when-downgrade"
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
            background: 'white'
          }}
        />
      )}
    </div>
  );
};


export default CodeComponent;