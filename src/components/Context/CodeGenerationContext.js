// CodeGenerationContext.js
"use client";
import {
  usePathname,
  useRouter,
  useSearchParams,
  useParams,
} from "next/navigation";
import React, {
  createContext,
  useState,
  useContext,
  useEffect,
  useCallback,
  useRef,
} from "react";

import { AlertContext } from "../NotificationAlertService/AlertList";
import { usePlanRestriction } from "./PlanRestrictionContext";
import {
  fetchTask,
  fetchBatchCallbackState,
  extractIpFromUrl,
} from "@/utils/api";
import Cookies from "js-cookie";
const CodeGenerationContext = createContext();

// Add these configuration constants at the top after imports
const MAX_ARRAY_SIZE = {
  MESSAGES: 200,
  FUNCTION_CALLS: 25,
  TERMINAL_OUTPUT: 50,
  DEBUG_MESSAGES: 50,
  RECENT_FUNCTION_CALLS: 20
};

export const CodeGenerationProvider = ({ children }) => {
  // UI States
  const { showAlert } = useContext(AlertContext);
  const [taskDetails, setTaskDetails] = useState(false);
  const [llmModel, setLlmModel] = useState("");
  const [isVisible, setIsVisible] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [isTerminalOpen, setIsTerminalOpen] = useState(true);
  const [isStepsModalOpen, setIsStepsModalOpen] = useState(false);
  const [isAutoTabSwitch, setIsAutoTabSwitch] = useState(false);
  const [messages, setMessages] = useState([]);
  const [newDocAlert, setNewDocAlert] = useState(false);
  const [tab, setTab] = useState("Status");
  const [primaryTab, setPrimaryTab] = useState("Status");
  const [secondaryTab, setSecondaryTab] = useState("");
  const [notification, setNotification] = useState({
    show: false,
    message: "",
  });
  const [activeTab, setActiveTab] = useState("Code");
  const [activeView, setActiveView] = useState('console');
  const [expandedTab, setExpandedTab] = useState(null);
  const [historicalFunctionCalls, setHistoricalFunctionCalls] = useState([]);
  const [historicalTerminalOutput, setHistoricalTerminalOutput] = useState([]);
  const isAutoTabSwitchRef = useRef(isAutoTabSwitch);
  const [steps, setSteps] = useState([]);
  const [isAiTyping, setIsAiTyping] = useState(false);
  //cost restriction
  const { setShowPlanRestriction, setCreditLimitCrossed } = usePlanRestriction();

  // Data States
  const [result, setResult] = useState([]);
  const [statusData, setStatusData] = useState(null);
  const [codeIframeUrl, setCodeIframeUrl] = useState("");
  const [currentIframeUrl, setCurrentIframeUrl] = useState(null);
  const [currentIp, setCurrentIp] = useState(null);
  const [status, setStatus] = useState("");
  const [gitStatus, setGitStatus] = useState({
    current_branch: "",
    status: "",
    timestamp: ""
  });
  const [functionCalls, setFunctionCalls] = useState([]);

  const [recentFunctionCalls, setRecentFunctionCalls] = useState([]);
  const [terminalOutput, setTerminalOutput] = useState([]);
  const [browserOutput, setBrowserOutput] = useState("");
  const [debugMessages, setDebugMessages] = useState([]);
  const [waitingForUserInput, setWaitingForUserInput] = useState(false);
  // WebSocket States
  const [wsConnection, setWsConnection] = useState(null);
  const [wsStatus, setWsStatus] = useState("disconnected");
  const [reconnectAttempt, setReconnectAttempt] = useState(0);
  const [architectureId, setArchitectureId] = useState(null)
  const maxReconnectAttempts = 5;

  // Task States
  const [taskStatus, setTaskStatus] = useState("RUNNING");
  const [stopLoading, setStopLoading] = useState(false);
  const [isStopped, setIsStopped] = useState(false);
  const [rollbackLoading, setrollbackLoading] = useState(false);
  const [error, setError] = useState(null);
  const [codeGenerationTaskId, setCodeGenerationTaskId] = useState(null);
  const [currentBrowserAction, setCurrentBrowserAction] = useState(null);
  const [isCodeMaintenance, setIsCodeMaintenance] = useState(false)
  const [timeoutWarning, setTimeoutWarning] = useState({
    show: false,
    secondsRemaining: 0
  });

  // Navigation
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const params = useParams();

  // FileWatch
  const [files, setFiles] = useState([]);
  const [selectedFile, setSelectedFile] = useState(null);
  const [latestStreamedFile, setLatestStreamedFile] = useState(null);
  const [incommingFileUpdates, setIncommingFileUpdates] = useState(null);
  const [diffHtml, setDiffHtml] = useState("");
  const [openFolders, setOpenFolders] = useState({});
  const [searchTerm, setSearchTerm] = useState("");
  const [streamedUpdates, setStreamedUpdates] = useState({});
  const [sessionName, setSessionName] = useState("Untitled")
  // Chat Interfaces states and refs
  const textAreaRef = useRef(null);
  const messagesContainerRef = useRef(null);
  const suggestionsDivRef = useRef(null);
  const [activeReplyTo, setActiveReplyTo] = useState(null);
  const [hasNewMessages, setHasNewMessages] = useState(false);
  const [documentContent, setDocumentContent] = useState(null)

  const [autoScroll, setAutoScroll] = useState(true);

  // Add new state for git command history
  const [gitCommandHistory, setGitCommandHistory] = useState([]);
  const [isReady, setIsReady] = useState(false);
  const [messagesCleared, setMessagesCleared] = useState(false);

  // Track readiness state per task to avoid unnecessary calls
  const [taskReadinessCache, setTaskReadinessCache] = useState({});
  const [lastReadinessCheck, setLastReadinessCheck] = useState(null);

  // Track current tab state to avoid readiness checks on maintenance tab
  const [currentActiveTab, setCurrentActiveTab] = useState('generation');

  // Code editor refresh state
  const [codeEditorKey, setCodeEditorKey] = useState(Date.now());

  //Repostories
  const [repositories, setRepositories] = useState([]);
  const [currentRepository, setCurrentRepository] = useState(null);

  // Smart readiness check function
  const performReadinessCheck = useCallback((taskId, ws, reason = 'connection') => {
    if (!taskId || !ws || ws.readyState !== WebSocket.OPEN) {
      console.log(`Skipping readiness check - invalid params: taskId=${taskId}, ws=${!!ws}, readyState=${ws?.readyState}`);
      return;
    }

    const now = Date.now();
    const cacheKey = taskId;
    const cachedReadiness = taskReadinessCache[cacheKey];

    // Don't call if we recently checked and it was ready (unless forced)
    const shouldSkip = cachedReadiness?.isReady &&
                      cachedReadiness?.timestamp &&
                      (now - cachedReadiness.timestamp) < 30000 && // 30 seconds cache
                      reason !== 'force' &&
                      reason !== 'reconnect';

    if (shouldSkip) {
      console.log(`✅ Skipping readiness check for task ${taskId} - recently checked and ready (${Math.round((now - cachedReadiness.timestamp) / 1000)}s ago)`);
      setIsReady(true);
      return;
    }

    console.log(`🔄 Performing readiness check for task ${taskId} - reason: ${reason}`);
    setLastReadinessCheck(now);

    ws.send(JSON.stringify({
      type: 'ready_check',
      user_id: Cookies.get('userId')
    }));
  }, [taskReadinessCache]);

  // Update readiness cache when we get a response
  const updateReadinessCache = useCallback((taskId, isReady) => {
    console.log(`📝 Updating readiness cache for task ${taskId}: ${isReady ? 'READY' : 'NOT READY'}`);
    setTaskReadinessCache(prev => ({
      ...prev,
      [taskId]: {
        isReady,
        timestamp: Date.now()
      }
    }));
  }, []);

  // Preview related Stuff
  const [previewContainersContext, setPreviewContainersContext] = useState({})
  const [containers, setContainers] = useState([]);
  const [selectedContainer, setSelectedContainer] = useState(null)
  // Update ref value whenever `isAutoTabSwitch` state changes

  const updatePreviewContext = (container_name, container_context) => {
    const task_id = searchParams.get("task_id");
    
    if (!task_id) return; // Early return if no task_id
    
    setPreviewContainersContext(prevContext => ({
      ...prevContext,
      [task_id]: {
        ...prevContext[task_id],
        [container_name]: container_context
      }
    }));
  };

  const updatePreviewWhole = (containers) => {
    const task_id = searchParams.get("task_id");
    
    if (!task_id) return; // Early return if no task_id
    let updatedContext = {}
    containers.forEach(element => {
      updatedContext[`${element.name}`] = element
    });

    setPreviewContainersContext({
      [task_id]: {
        ...updatedContext
      }
    })
  };

  useEffect(() => {
    isAutoTabSwitchRef.current = isAutoTabSwitch;
  }, [isAutoTabSwitch]);

  // Helper function to limit array size
  const limitArraySize = useCallback((array, maxSize) => {
    return array.length <= maxSize ? array : array.slice(array.length - maxSize);
  }, []);

  // Modify setMessages to limit array size
  const setMessagesWithLimit = useCallback((messagesOrUpdater) => {
    if (typeof messagesOrUpdater === 'function') {
      setMessages(prev => limitArraySize(messagesOrUpdater(prev), MAX_ARRAY_SIZE.MESSAGES));
    } else {
      setMessages(limitArraySize(messagesOrUpdater, MAX_ARRAY_SIZE.MESSAGES));
    }
  }, [limitArraySize]);

  // Helper function to update or add a message
  const updateMessage = useCallback((incomingMessage, messageType) => {
    setMessages((prevMessages) => {
      // Check if this message already exists
      const existingMsgIndex = prevMessages.findIndex(
        (msg) => msg.id === incomingMessage.id
      );

      // If it's a new message
      if (existingMsgIndex === -1) {
        // Don't add system messages except deployment_status
        if (incomingMessage.msg_type === 'system' && incomingMessage.type !== 'deployment_status') {
          return prevMessages;
        }
        return limitArraySize([...prevMessages, incomingMessage], MAX_ARRAY_SIZE.MESSAGES);
      }
      // Update existing message
      else {
        const updatedMessages = [...prevMessages];
        const oldMessage = updatedMessages[existingMsgIndex];

        // If we're streaming, determine if content should be appended or replaced
        const shouldAppend = incomingMessage.status === 'streaming' && messageType !== 'agent_message_streaming';

        // For agent_message_streaming, we always replace the content
        const newContent = messageType === 'agent_message_streaming'
          ? incomingMessage.content
          : shouldAppend
            ? (oldMessage.content || '') + (incomingMessage.content || '')
            : incomingMessage.content;

        updatedMessages[existingMsgIndex] = {
          ...oldMessage,
          ...incomingMessage,
          content: newContent,
          // Keep existing user_details if not in the incoming message
          user_details: incomingMessage.user_details || oldMessage.user_details
        };

        return limitArraySize(updatedMessages, MAX_ARRAY_SIZE.MESSAGES);
      }
    });
  }, [limitArraySize]);

  // Optimize useEffect for messages
  const debouncedMessagesUpdate = useRef(null);
  useEffect(() => {
    if (debouncedMessagesUpdate.current) {
      clearTimeout(debouncedMessagesUpdate.current);
    }
    debouncedMessagesUpdate.current = setTimeout(() => {
      // Any operations that need to happen when messages change
      debouncedMessagesUpdate.current = null;
    }, 300);
  }, [messages]);

  useEffect(() => { }, [messages]);


  const fetchInitialMessages = useCallback(async (taskId) => {
   if (!taskId) return;
    try {
      const response = await fetchBatchCallbackState("messages", taskId);
      if (response && response.messages) {
        // Filter out system messages except for deployment_status
        const filteredMessages = response.messages.filter(msg =>
          msg.msg_type !== 'system' ||
          msg.type === 'deployment_status'
        );
        setMessagesWithLimit(filteredMessages);
        setWaitingForUserInput(response.waiting_for_user || false);
      }
    } catch (error) {

      setError("Failed to fetch message history");
    }
  }, [setMessagesWithLimit]);

  // CodeGenerationContext.js
  const fetchHistoricalFunctionCalls = useCallback(async () => {
    try {
      const taskId = searchParams.get("task_id");
      if (!taskId) {
        return;
      }

      // Check if we already have data to avoid refetching
      if (historicalFunctionCalls.length > 0) return;

      const response = await fetchBatchCallbackState("function_calls", taskId);

      // Extract function_calls from the response
      let formattedData = [];
      if (response && response.function_calls) {
        formattedData = response.function_calls;
      } else if (Array.isArray(response) && response[0]?.function_calls) {
        formattedData = response[0].function_calls;
      }

      setHistoricalFunctionCalls(limitArraySize(formattedData, MAX_ARRAY_SIZE.FUNCTION_CALLS));
    } catch (error) {

      setHistoricalFunctionCalls([]);
      setError("Failed to fetch historical function calls");
    }
  }, [searchParams, historicalFunctionCalls.length, limitArraySize]);

  const fetchHistoricalTerminalOutput = useCallback(async () => {
    try {
      const taskId = searchParams.get("task_id");
      if (!taskId) {
        return;
      }

      const response = await fetchBatchCallbackState("terminal", taskId);

      // Extract terminal_output from the response
      let formattedData = [];
      if (response && response.terminal_output) {
        formattedData = response.terminal_output;
      } else if (Array.isArray(response) && response[0]?.terminal_output) {
        formattedData = response[0].terminal_output;
      }

      setHistoricalTerminalOutput(formattedData);
    } catch (error) {

      setHistoricalTerminalOutput([]);
      setError("Failed to fetch historical terminal output");
    }
  }, [searchParams]);

  const handleTabSwitch = useCallback(
    (tabName) => {
      if (!isAutoTabSwitch) return;
      // setActiveTab(tabName);

      // Handle special cases for specific tabs
      if (tabName === "Function Calls") {
        fetchHistoricalFunctionCalls();
      } else if (tabName === "Terminal") {
        fetchHistoricalTerminalOutput();
      }

      // showNotification(`Switching to ${tabName} tab`);
    },
    [
      isAutoTabSwitch,
      fetchHistoricalFunctionCalls,
      fetchHistoricalTerminalOutput,
    ]
  );

  // Create a shared notification function
  const showNotification = useCallback((message) => {
    setNotification({ show: true, message });
    setTimeout(() => {
      setNotification({ show: false, message: "" });
    }, 2000);
  }, []);

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Clear file-related states when tab becomes hidden
        setSelectedFile(null);
        setLatestStreamedFile(null);
        setDiffHtml(null);
        setStreamedUpdates({});
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // WebSocket connection handler
  const connectWebSocket = useCallback(
    (taskId) => {
      if (!taskId) return;

      setWsStatus("connecting");
      const ws = new WebSocket(`${process.env.NEXT_PUBLIC_WS_URL}/${taskId}`);

      ws.onopen = () => {
        setWsStatus("connected");
        setReconnectAttempt(0);

        // Immediately request container status when connected
        if (taskId) {
          setTimeout(() => {
            if (ws.readyState === WebSocket.OPEN) {
              ws.send(JSON.stringify({
                type: "get_all_containers",
                task_id: taskId,
                input_data: {}
              }));
            }
          }, 500); // Small delay to ensure connection is stable
        }

        // Use smart readiness check instead of always calling
        const reason = reconnectAttempt > 0 ? 'reconnect' : 'connection';
        performReadinessCheck(taskId, ws, reason);
      };

      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        const timestamp = new Date().toISOString();




        switch (data.type) {

          case "ready_check":
            setIsReady(true);
            // Update cache with successful readiness check
            const taskId = searchParams.get("task_id");
            if (taskId) {
              updateReadinessCache(taskId, true);
            }
            break;

          case "document_update":
            if (data.data && data.data.content) {
              // Create a document object with title from file path and content
              const newDocument = {
                title: data.data.file_path ? data.data.file_path.split('/').pop() : 'Document',
                content: data.data.content,
                timestamp: new Date().toISOString() // Add timestamp to help with ordering
              };

              // Update the document content in state
              // This will trigger the useEffect in DocumentsRenderPanel to add this to the list
              setDocumentContent(newDocument);

              // Switch to Document tab
              setActiveTab("Document");

              // Reset newDocAlert to false initially
              setNewDocAlert(false);

              // Then set it to true to show notification
              setTimeout(() => {
                setNewDocAlert(true);
              }, 100);

              // Show notification
              showAlert("New document received: " + newDocument.title, "info");
            }
            break;


          case "status_update":
            setStatus(data.status);
            setPrimaryTab("Status");
            setSecondaryTab("Status");
            setExpandedTab(null);

            const status = data.status;
            setStatus(status);
            if (status == "stopped") {
              setIsStopped(true);
              setStopLoading(true);
            }
            if (data.data && data.data.type == "waiting_for_input") {
              setWaitingForUserInput(true);
            }
            if (data.data && data.data?.type == "rollback") {
              showAlert("Reverted last step successfully!", "success");
            }

            // Add new condition for running status
            if (status === "running") {
              // Check for duplicates before adding message
              const lastFewMessages = messages.slice(-3);
              const statusContent = "🚀 Code generation process has started. AI agents are now actively working on your request...";

              const isDuplicate = lastFewMessages.some(
                (msg) =>
                  msg.type === "status_update" &&
                  msg.content === statusContent &&
                  Math.abs(new Date(msg.timestamp) - new Date(timestamp)) < 2000
              );

              if (!isDuplicate) {
                updateMessage({
                  id: Date.now(),
                  type: "status_update",
                  content: statusContent,
                  sender: "System",
                  timestamp,
                }, 'status_update');
              }
            }
            break;

          case "agent_message":
            if (data.data) {
              // Handle streaming status specifically
              if (data.data.status === "streaming") {
                setIsAiTyping(true);

                // Check if we already have a streaming message
                const existingStreamingMsg = messages.find(
                  m => m.msg_type === 'llm' && m.status === 'streaming'
                );

                if (existingStreamingMsg) {
                  // Update existing streaming message - REPLACE content instead of appending
                  updateMessage({
                    ...data.data,
                    id: existingStreamingMsg.id, // Keep the same ID for updates
                    content: data.data.content, // Replace content completely
                    status: 'streaming'
                  }, 'agent_message_streaming');
                } else {
                  // Create a new streaming message
                  updateMessage({
                    ...data.data,
                    status: 'streaming'
                  }, 'agent_message_streaming');
                }
              } else {
                // Handle regular agent messages
                let newMessage = {
                  ...data.data,
                  timestamp: data.data.timestamp || new Date().toISOString()
                };
                updateMessage(newMessage, data.type);

                // If this completes a message, stop AI typing indicator
                if (data.data.status === 'completed' || data.data.status === 'done') {
                  setIsAiTyping(false);
                }
              }
            }
            break;

          case "user_message":
            if (data.data) {
              let newMessage = {
                ...data.data,
                timestamp: data.data.timestamp || new Date().toISOString()
              };
              updateMessage(newMessage, data.type);
            }
            break;

          case "git_status_update":
            if (data.data) {
              setGitStatus({
                current_branch: data.data.current_branch,
                status: data.data.status,
                timestamp: data.data.timestamp
              });
            }
            break;
            
            case "timeout_warning":
              if (data.data && data.data.seconds_remaining !== undefined) {
                // Check if the session has been terminated
                if (data.data.terminated === true) {
                  // Session has been terminated due to inactivity
                  setStatus("Auto Stopped");
                  setTimeoutWarning({
                    show: true,
                    secondsRemaining: 0,
                    terminated: true // Add this flag to indicate termination
                  });
                  let task_id = searchParams.get("task_id");
                  if (task_id) {
                      // If task_id exists, remove it from the URL
                      let organizationId = params.organization_id;
                      let projectId = params.projectId;
                      const newSearchParams = new URLSearchParams(searchParams);
                      newSearchParams.delete("task_id");

                      router.push(`/${organizationId}/project/${projectId}/code`);
                      sessionStorage.setItem("session_terminated", "true");
                      sessionStorage.setItem("session_terminated_task_id", task_id);
                  }
                  // In session storage set a flag to indicate session termination
                } else {
                  // Normal timeout warning - session still active
                  setTimeoutWarning({
                    show: true,
                    secondsRemaining: data.data.seconds_remaining,
                    terminated: false
                  });
                }
              }
              break;

          case "input_received":
            if (data.data) {
              // Check for duplicates
              const lastFewMessages = messages.slice(-3);
              const isDuplicate = lastFewMessages.some(
                (msg) =>
                  msg.content === data.data.content &&
                  msg.sender === data.data.sender &&
                  Math.abs(
                    new Date(msg.timestamp || timestamp) -
                    new Date(data.data.timestamp || timestamp)
                  ) < 2000
              );

              if (!isDuplicate) {
                updateMessage({
                  id: Date.now(),
                  type: "user",
                  content: data.data.content,
                  sender: "User",
                  timestamp: data.data.timestamp || timestamp,
                }, 'input_received');
              }
            }
            setWaitingForUserInput(false);
            setPrimaryTab("Status");
            setSecondaryTab("Status");
            setExpandedTab(null);
            break;

          case "terminal_output":
            setTerminalOutput((prev) =>
              limitArraySize([...prev, { output: data.data.output }], MAX_ARRAY_SIZE.TERMINAL_OUTPUT)
            );
            break;


          case "browser_output":
            if (data.data && data.data.output) {
              showAlert("Visual Verification In Progress", "info");
              setActiveTab("Preview");
              setActiveView("browser");
              setBrowserOutput(data.data.output);
            }
            break;

          case "deployment_status":
            if (data.data) {

              // Auto-switching for deployment status with tab checking
              if (isAutoTabSwitchRef.current && activeTab !== "Status") {
                setActiveTab("Status");
                showAlert("Switching to Status tab for deployment updates", "info");
              }

              const deploymentStatusContent = `🚀 Deployment Status: ${data.data.deployment_type} - ${data.data.status}${data.data.command ? ` (Command: ${data.data.command})` : ''}`;

              // Check if this is a duplicate message we've received in the last few seconds
              const lastFewMessages = messages.slice(-5);
              const isDuplicate = lastFewMessages.some(
                (msg) =>
                  msg.type === "deployment_status" &&
                  msg.deployment_data?.id === data.data.id &&
                  Math.abs(new Date(msg.timestamp) - new Date()) < 5000
              );

              if (!isDuplicate) {

                updateMessage({
                  id: Date.now(),
                  type: "deployment_status",
                  content: deploymentStatusContent,
                  sender: "System",
                  deployment_data: data.data,
                  timestamp: new Date().toISOString(),
                  msg_type: "system_message", // Use a type that won't be filtered out
                }, 'deployment_status');
              }
            }
            break;

          case "progress_update":
            if (
              data.data?.status?.includes(
                "Starting task execution process for CodeGeneration"
              )
            ) {
              // Check for duplicates
              const lastFewMessages = messages.slice(-3);
              const progressContent =
                "🔄 Initializing AI-powered code generation system. Setting up the development environment and preparing necessary tools...";

              const isDuplicate = lastFewMessages.some(
                (msg) =>
                  msg.type === "progress_update" &&
                  msg.content === progressContent &&
                  Math.abs(new Date(msg.timestamp) - new Date(timestamp)) < 2000
              );

              if (!isDuplicate) {
                updateMessage({
                  id: Date.now(),
                  type: "progress_update",
                  content: progressContent,
                  sender: "System",
                  timestamp,
                }, 'progress_update');
              }
            }
            break;
          case "task_start":
            setExpandedTab(null);
            break;

          case "file_watch":
            if (data.data) {
              setIncommingFileUpdates(data.data);
            }
            break

          case "cost_update":
            if (data.data.limit_crossed) {
              setShowPlanRestriction(true);
              setCreditLimitCrossed(true);
            }

          case "message_resolved":
            // AI finished typing
            if (data.data.msg_type === 'llm') {
              setIsAiTyping(false);
            }
            // Use our helper function to update the message
            updateMessage(data.data, data.type);
            break;
          

          case "containers":
            if (data.type == 'containers' && data.data?.containers) {
                updatePreviewWhole(data.data?.containers)
                setContainers(data.data.containers);
            }
            break;

          case "get_all_containers_response":
            if (data.data?.status === 'success' && data.data?.containers) {
              updatePreviewWhole(data.data.containers);
              setContainers(data.data.containers);
              
              // Auto-select first container if none is selected
              if (!selectedContainer && data.data.containers.length > 0) {
                setSelectedContainer(data.data.containers[0].name);
              }
            }
            break;

          case "container_statuses":
            if (data.data?.status === 'success' && data.data?.container_statuses) {
              const statuses = data.data.container_statuses;
              
              // Update containers with new status information
              setContainers(prevContainers => 
                prevContainers.map(container => {
                  const statusInfo = statuses[container.name];
                  if (statusInfo) {
                    return {
                      ...container,
                      status: statusInfo.status,
                      url: statusInfo.url || container.url,
                      timestamp: statusInfo.timestamp,
                      error: statusInfo.error
                    };
                  }
                  return container;
                })
              );

              // Update preview context with latest statuses
              const task_id = searchParams.get("task_id");
              if (task_id) {
                Object.entries(statuses).forEach(([containerName, statusInfo]) => {
                  updatePreviewContext(containerName, statusInfo);
                });
              }

              // Update selected container if it has new status
              const selectedContainerStatus = statuses[selectedContainer];
              if (selectedContainerStatus) {
                setContainers(prev => 
                  prev.map(c => c.name === selectedContainer 
                    ? { ...c, ...selectedContainerStatus } 
                    : c
                  )
                );
              }
            }
            break;

          case "container_status":
            if (data.data?.container_status) {
              const containerStatus = data.data.container_status;
              
              // Update the specific container
              setContainers(prevContainers =>
                prevContainers.map(container =>
                  container.name === containerStatus.name
                    ? { ...container, ...containerStatus }
                    : container
                )
              );

              // Update preview context
              updatePreviewContext(containerStatus.name, containerStatus);
            }
            break;

          case "run_all_containers_response":
            if (data.data?.status === 'completed' && data.data?.result) {
              // Update all container statuses from result
              Object.entries(data.data.result).forEach(([containerName, containerData]) => {
                updatePreviewContext(containerName, containerData);
              });
              
              // Request fresh container list
              if (wsConnection?.readyState === WebSocket.OPEN) {
                const taskId = searchParams.get("task_id");
                if (taskId) {
                  setTimeout(() => {
                    wsConnection.send(JSON.stringify({
                      type: "get_all_status",
                      task_id: taskId,
                      input_data: {}
                    }));
                  }, 1000);
                }
              }
            }
            break;

          case "preview_restart_response":
            if (data.data?.status === 'completed' && data.data?.result) {
              const containerData = data.data.result;
              
              // Update specific container
              setContainers(prevContainers =>
                prevContainers.map(container =>
                  container.name === containerData.name
                    ? { ...container, ...containerData }
                    : container
                )
              );
              
              // Update preview context
              updatePreviewContext(containerData.name, containerData);
            }
            break;

          case "preview_error":
            if (data.message) {
              showAlert(`Preview Error: ${data.message}`, "error");
            }
            break;
          
          default:
        }
      };
      ws.onerror = (error) => {

        setError("WebSocket connection error");
        setWsStatus("error");
      };

      ws.onclose = () => {
        setWsStatus("disconnected");
        setIsReady(false);
        // Clear readiness cache for this task when connection is lost
        const taskId = searchParams.get("task_id");
        if (taskId) {
          updateReadinessCache(taskId, false);
        }
      };

      setWsConnection(ws);

      return () => {
        if (ws) {
          ws.close();
          setWsConnection(null);
        }
      };
    },
    [isAutoTabSwitch, reconnectAttempt, maxReconnectAttempts]
  );

  const reconnectWebSocket = useCallback(
    (taskId) => {
      if (wsConnection) {
        wsConnection.close();
        setWsConnection(null);
      }
      setReconnectAttempt(0);
      return connectWebSocket(taskId);
    },
    [wsConnection, connectWebSocket]
  );

  const fetchCurrentUrl = useCallback(async () => {
    try {
      if (!searchParams.get("task_id")) {
        return;
      }

      const _data = await fetchTask(searchParams.get("task_id"));
      setTaskDetails(_data);
      if (_data.iframe) {
        // Extract IP from the iframe URL or use the provided IP
        const extractedIp = _data.ip || extractIpFromUrl(_data.iframe);
        setCurrentIp(extractedIp);
        setCurrentIframeUrl(_data.iframe);
      }

      if (_data.architecture_id) {
        setArchitectureId(_data.architecture_id);
      }

      if (_data.session_name) {
        setSessionName(_data.session_name);
      }

    } catch (error) {

    }
  }, [searchParams]);


  const formatWorkspaceUrl = (ip, portNumber, taskId) => {
    if (!ip) {

      try {
        fetchCurrentUrl();
      } catch (error) {

      }
      return null;
    }

    const formattedIp = ip.replace(/\./g, '_');

    if (portNumber != 8080) {
      return `https://${portNumber}_${formattedIp}.workspace.develop.kavia.ai`
    }
    if (taskId && ((taskId.startsWith("deep-query") || taskId.startsWith("cm")))) {
      const finalUrl = `https://${portNumber}_${formattedIp}.workspace.develop.kavia.ai/?folder=/home/<USER>/workspace/${taskId}`;

      return finalUrl;
    }
    const finalUrl = `https://${portNumber}_${formattedIp}.workspace.develop.kavia.ai/?folder=/home/<USER>/workspace`;


    return finalUrl;
  };

  // Code editor refresh function
  const refreshCodeEditor = useCallback(() => {
    setCodeEditorKey(Date.now());
  }, []);

  // Refresh containers function
  const refreshContainers = useCallback(() => {
    if (wsConnection?.readyState === WebSocket.OPEN) {
      const taskId = searchParams.get("task_id");
      if (taskId) {
        wsConnection.send(JSON.stringify({
          type: "get_all_containers",
          task_id: taskId,
          input_data: {}
        }));
      }
    }
  }, [wsConnection, searchParams]);

  // Force readiness check function - for manual refresh or when status might have changed
  const forceReadinessCheck = useCallback(() => {
    const taskId = searchParams.get("task_id");
    if (taskId && wsConnection?.readyState === WebSocket.OPEN) {
      performReadinessCheck(taskId, wsConnection, 'force');
    }
  }, [wsConnection, searchParams, performReadinessCheck]);

  // Function to update current active tab from external components
  const updateActiveTab = useCallback((tab) => {
    setCurrentActiveTab(tab);
  }, []);

  // Smart readiness check with caching and tab awareness
  useEffect(() => {
    const taskId = searchParams.get("task_id");

    // Only run readiness check when:
    // 1. We have a task ID
    // 2. We're on the generation tab (not maintenance tab or history modal)
    // 3. We haven't already checked this task recently
    if (taskId && currentActiveTab === 'generation') {
      const cacheKey = `${taskId}_readiness`;
      const cachedResult = taskReadinessCache[cacheKey];
      const now = Date.now();

      // Skip if we have a recent successful readiness check (within 30 seconds)
      if (cachedResult && cachedResult.isReady && (now - cachedResult.timestamp) < 30000) {
        console.log('Skipping readiness check - recently verified as ready');
        return;
      }

      console.log('Running readiness check for task:', taskId);
      checkReadiness(taskId).then(isReady => {
        // Cache the result
        setTaskReadinessCache(prev => ({
          ...prev,
          [cacheKey]: {
            isReady,
            timestamp: now
          }
        }));
      });
    }
  }, [searchParams, checkReadiness, taskReadinessCache, currentActiveTab]);

    // Add an effect to handle task_id changes - only when on generation tab
  useEffect(() => {
    const taskId = searchParams.get("task_id");
    // Check if we're on the main code page (not history modal or maintenance subpage)
    const currentPath = window.location.pathname;
    const isOnCodePage = currentPath.includes('/code') && !currentPath.includes('/maintenance');

    if (taskId && isOnCodePage) {
      fetchCurrentUrl();
      // Ensure codeGenerationTaskId is always in sync with URL
      if (taskId !== codeGenerationTaskId) {
        setCodeGenerationTaskId(taskId);
      }
    }
  }, [searchParams, fetchCurrentUrl, codeGenerationTaskId]);

  useEffect(() => {
    const taskId = searchParams.get("task_id");
    // Check if we're on the main code page (not history modal or maintenance subpage)
    const currentPath = window.location.pathname;
    const isOnCodePage = currentPath.includes('/code') && !currentPath.includes('/maintenance');

    if (taskId && isOnCodePage) {
      setIsVisible(true);
      setTaskStatus("RUNNING");
      setIsStopped(false);
      setCodeGenerationTaskId(taskId);
      setReconnectAttempt(0);
      setMessagesCleared(false); // Allow messages to be loaded for new task

      // Only establish WebSocket connection when on code page
      connectWebSocket(taskId);
    }
  }, [searchParams, connectWebSocket]);

  // Effect for handling task ID changes - only load messages when on generation tab
  useEffect(() => {
    const taskId = searchParams.get("task_id");
    const currentPath = window.location.pathname;
    const isOnGenerationTab = !currentPath.includes('/history') && !currentPath.includes('/maintenance');

    const loadInitialStates = async () => {
      try {
        // Only load messages history - other data will be loaded on demand
        await fetchInitialMessages(taskId);
      } catch (error) {

        setError("Failed to load initial state");
      }
    };

    if (taskId && isOnGenerationTab && wsConnection && wsStatus === "connected" && !messagesCleared) {
      loadInitialStates();
    } else if (taskId && isOnGenerationTab && wsStatus != "connected" && messages.length == 0 && !messagesCleared) {
      loadInitialStates();
    }
  }, [
    wsConnection,
    wsStatus,
    searchParams,
    fetchInitialMessages,
    messagesCleared
  ]);

  // Periodic readiness check for long-running sessions (every 5 minutes)
  useEffect(() => {
    const taskId = searchParams.get("task_id");
    const currentPath = window.location.pathname;
    const isOnGenerationTab = !currentPath.includes('/history') && !currentPath.includes('/maintenance');

    if (!taskId || !isOnGenerationTab || wsStatus !== "connected") return;

    const interval = setInterval(() => {
      if (wsConnection?.readyState === WebSocket.OPEN) {
        performReadinessCheck(taskId, wsConnection, 'periodic');
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [wsConnection, wsStatus, performReadinessCheck, searchParams]);

  // Listen for refresh events to trigger readiness check
  useEffect(() => {
    const handleRefreshGeneration = () => {
      const currentPath = window.location.pathname;
      const isOnGenerationTab = !currentPath.includes('/history') && !currentPath.includes('/maintenance');

      if (isOnGenerationTab) {
        forceReadinessCheck();
      }
    };

    const handleSwitchToGeneration = () => {
      // When switching to generation tab, check if we need to establish connection
      const taskId = searchParams.get("task_id");
      if (taskId) {
        // If no connection, establish it
        if (!wsConnection || wsConnection.readyState !== WebSocket.OPEN) {
          connectWebSocket(taskId);
        } else {
          // If connection exists, force readiness check
          forceReadinessCheck();
        }
      }
    };

    // Listen for custom events
    window.addEventListener('refreshGeneration', handleRefreshGeneration);
    window.addEventListener('switchToGeneration', handleSwitchToGeneration);

    return () => {
      window.removeEventListener('refreshGeneration', handleRefreshGeneration);
      window.removeEventListener('switchToGeneration', handleSwitchToGeneration);
    };
  }, [forceReadinessCheck, connectWebSocket, wsConnection, searchParams]);
  // UI handlers
  const toggleDropdown = () => setIsDropdownOpen(!isDropdownOpen);
  const toggleFullScreen = () => setIsFullScreen(!isFullScreen);
  const toggleTerminal = () => setIsTerminalOpen(!isTerminalOpen);
  const toggleStepsModal = () => setIsStepsModalOpen(!isStepsModalOpen);
  const toggleAutoTabSwitch = () => setIsAutoTabSwitch(!isAutoTabSwitch);
  const changeTab = (newTab) => setTab(newTab);

  const openModal = () => setIsVisible(true);
  const closeModal = () => {
    console.log("closeModal called - setting isVisible to false");
    // Disconnect WebSocket
    if (wsConnection) {
      wsConnection.close();
      setWsConnection(null);
    }

    // Reset WebSocket states
    setWsStatus("disconnected");
    setReconnectAttempt(0);
    setMessages([]);
    setMessagesCleared(true);

    // Clear all data states
    setResult([]);
    setStatusData(null);
    setCodeIframeUrl("");
    setCurrentIframeUrl(null);
    setStatus("");
    setFunctionCalls([]);
    setRecentFunctionCalls([]);
    setTerminalOutput([]);
    setBrowserOutput("");
    setDebugMessages([]);
    setGitStatus({
      current_branch: "",
      status: "",
      timestamp: ""
    });
    setSessionName("");
    // Reset task states
    setTaskStatus("RUNNING");
    setStopLoading(false);
    setIsStopped(false);
    setrollbackLoading(false);
    setError(null);
    setCodeGenerationTaskId(null);
    setIsAiTyping(false);

    // Reset UI states
    console.log("closeModal - setting isVisible to false");
    setIsVisible(false);
    setIsDropdownOpen(false);
    setIsFullScreen(false);
    setIsTerminalOpen(true);
    setIsStepsModalOpen(false);
    setIsAutoTabSwitch(false);
    setMessages([]);
    setTab("Code");
    setPrimaryTab("Code");
    setSecondaryTab("");
    setNotification({ show: false, message: "" });
    setActiveTab("Code");
    setActiveView('console');
    setExpandedTab(null);

    // Reset FileWatch states
    setFiles([]);
    setSelectedFile(null);
    setLatestStreamedFile(null);
    setIncommingFileUpdates(null);
    setDiffHtml("");
    setOpenFolders({});
    setSearchTerm("");
    setStreamedUpdates({});

    // Reset Historical states
    setHistoricalFunctionCalls([]);
    setHistoricalTerminalOutput([]);

    setArchitectureId(null);
    setIsReady(false);

    // Clear readiness cache
    setTaskReadinessCache({});
    setLastReadinessCheck(null);

    // Reset git command history
    setGitCommandHistory([]);

    // Clear chat interface states and refs
    textAreaRef.current = null;
    messagesContainerRef.current = null;
    suggestionsDivRef.current = null;
    setActiveReplyTo(null);
    setHasNewMessages(false);
    setAutoScroll(true);

    // Reset preview container states
    setPreviewContainersContext({});
    setContainers([]);
    setSelectedContainer(null);

    // Update URL
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.delete("task_id");
    newSearchParams.delete("session_name");
    router.push(`${pathname}?${newSearchParams.toString()}`);

  };

  const clearDebugMessages = () => {
    setDebugMessages([]);
  };

  // Optimize debug messages
  const addDebugMessage = useCallback((message) => {
    setDebugMessages(prev =>
      limitArraySize([...prev, message], MAX_ARRAY_SIZE.DEBUG_MESSAGES)
    );
  }, [limitArraySize]);

  useEffect(() => {
    if (!isVisible) {
      // Reset WebSocket states
      if (wsConnection) {
        wsConnection.close();
        setWsConnection(null);
      }
      setWsStatus("disconnected");
      setReconnectAttempt(0);

      // Reset UI states
      setLlmModel("");
      setIsDropdownOpen(false);
      setIsFullScreen(false);
      setIsTerminalOpen(true);
      setIsStepsModalOpen(false);
      setIsAutoTabSwitch(false);
      setMessages([]);
      setMessagesCleared(true);
      setTab("Code");
      setPrimaryTab("Code");
      setSecondaryTab("");
      setNotification({ show: false, message: "" });
      setActiveTab("Code");
      setActiveView('console');
      setExpandedTab(null);
      setIsAiTyping(false);

      // Reset Data states
      setResult([]);
      setStatusData(null);
      setCodeIframeUrl("");
      setCurrentIframeUrl(null);
      setCurrentIp(null);
      setStatus("");
      setFunctionCalls([]);
      setRecentFunctionCalls([]);
      setTerminalOutput([]);
      setBrowserOutput("");
      setDebugMessages([]);
      setWaitingForUserInput(false);

      // Reset Task states
      setTaskStatus("");
      setStopLoading(false);
      setIsStopped(false);
      setrollbackLoading(false);
      setError(null);
      setCodeGenerationTaskId(null);
      setCurrentBrowserAction(null);

      // Reset FileWatch states
      setFiles([]);
      setSelectedFile(null);
      setLatestStreamedFile(null);
      setIncommingFileUpdates(null);
      setDiffHtml("");
      setOpenFolders({});
      setSearchTerm("");
      setStreamedUpdates({});

      // Reset Historical states
      setHistoricalFunctionCalls([]);
      setHistoricalTerminalOutput([]);

      setArchitectureId(null);
      setIsReady(false);
      setMessagesCleared(true); // Mark messages as cleared
  
      // Reset preview container states
      setPreviewContainersContext({});
      setContainers([]);
      setSelectedContainer(null);
    }
  }, [isVisible]);

  return (
    <CodeGenerationContext.Provider
      value={{
        // UI values
        llmModel,
        setLlmModel,
        isVisible,
        setIsVisible,
        isDropdownOpen,
        isFullScreen,
        isTerminalOpen,
        isStepsModalOpen,
        tab,
        toggleDropdown,
        toggleFullScreen,
        toggleTerminal,
        toggleStepsModal,
        changeTab,
        openModal,
        closeModal,
        messages,
        setMessages: setMessagesWithLimit,
        updateMessage,
        recentFunctionCalls,
        setRecentFunctionCalls,
        newDocAlert,
        setNewDocAlert,
        isAiTyping,
        setIsAiTyping,

        setStatus,

        // Task values
        taskStatus,
        setTaskStatus,
        setIsStopped,
        isStopped,
        stopLoading,
        error,
        setError,
        setCodeIframeUrl,
        codeIframeUrl,
        statusData,
        setStatusData,
        setResult,
        result,
        rollbackLoading,
        currentIframeUrl,
        setCurrentIframeUrl,
        currentIp,
        setCurrentIp,

        isAutoTabSwitch,
        toggleAutoTabSwitch,

        // WebSocket values
        status,
        functionCalls,
        terminalOutput,
        browserOutput,
        wsConnection,
        wsStatus,
        reconnectWebSocket,
        reconnectAttempt,
        maxReconnectAttempts,

        //Architecture Id
        architectureId,
        setArchitectureId,

        //Debug messages
        debugMessages,
        clearDebugMessages,
        addDebugMessage,

        //notification
        setWaitingForUserInput,
        waitingForUserInput,

        // FileWatch
        files,
        setFiles,
        selectedFile,
        setSelectedFile,
        latestStreamedFile,
        setLatestStreamedFile,
        diffHtml,
        setDiffHtml,
        openFolders,
        setOpenFolders,
        searchTerm,
        setSearchTerm,
        streamedUpdates,
        setStreamedUpdates,
        incommingFileUpdates, setIncommingFileUpdates,
        fetchCurrentUrl,
        primaryTab,
        setPrimaryTab,
        secondaryTab,
        setSecondaryTab,
        notification,
        showNotification,
        expandedTab,
        setExpandedTab,
        historicalFunctionCalls,
        fetchHistoricalFunctionCalls,
        handleTabSwitch,
        historicalTerminalOutput,
        fetchHistoricalTerminalOutput,
        activeTab,
        setActiveTab,
        currentBrowserAction,
        setCurrentBrowserAction,
        steps,
        setSteps,
        gitStatus,
        setGitStatus,
        formatWorkspaceUrl,
        // Add new git command history values
        gitCommandHistory,
        setGitCommandHistory,

        // Refs
        textAreaRef,
        messagesContainerRef,
        suggestionsDivRef,
        //Repositories
        repositories,
        setRepositories,
        currentRepository,
        setCurrentRepository,
        // Chat Interfaces states and refs
        activeReplyTo,
        setActiveReplyTo,
        hasNewMessages,
        setHasNewMessages,
        autoScroll,
        setAutoScroll,
        setIsCodeMaintenance,
        isCodeMaintenance,
        isReady,
        setIsReady,
        sessionName,
        setSessionName,

        // Code editor refresh
        codeEditorKey,
        refreshCodeEditor,

        // Readiness check
        forceReadinessCheck,

        //Active View
        activeView,
        setActiveView,
        documentContent,
        setDocumentContent,
        taskDetails,

        // inactivity
        setTimeoutWarning,
        timeoutWarning,

        // Task ID
        currentTaskId: codeGenerationTaskId,
        setCurrentTaskId: setCodeGenerationTaskId,

        // preview containers
        previewContainersContext,
        setPreviewContainersContext,
        containers,
        setContainers,
        selectedContainer,
        setSelectedContainer,
        refreshContainers,

        // Tab management
        updateActiveTab
      }}
    >
      {children}
    </CodeGenerationContext.Provider>
  );
};

export const useCodeGeneration = () => useContext(CodeGenerationContext);
