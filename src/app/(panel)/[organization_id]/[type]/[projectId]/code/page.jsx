"use client";

import React, { useEffect, useState, useCallback, useRef } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams, usePathname } from "next/navigation";
import { RefreshCcw, RefreshCw } from 'lucide-react';
import Sessions from "@/components/Sessions/Sessions";
import { getPastCodeTasks } from "@/utils/batchAPI";
import Pagination from "@/components/UIComponents/Paginations/Pagination";
import { TOOLTIP_CONTENT } from "@/utils/constant/tooltip";
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";
import dynamic from 'next/dynamic';
import ContainerList from "@/components/BrowsePanel/Architecture/ContainerList"
import dayjs from 'dayjs';
import CodeGenerationModal from "@/app/modal/CodeGenerationModal";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import { useUser } from "@/components/Context/UserContext";
import { transformSessionsResponse } from "@/utils/sessionUtils";
const MaintenancePage = dynamic(() => import('./maintenance/page'));


const CodePage = () => {
  const { is_having_permission } = useUser();
  const { isVisible, setIsVisible, updateActiveTab } = useCodeGeneration();
  const { projectId } = useParams();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [sessions, setSessions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('generation');
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);

  // Add ref to prevent unnecessary tab updates
  const activeTabRef = useRef('generation');

  // Keep ref in sync with state and update context
  useEffect(() => {
    activeTabRef.current = activeTab;
    // Update the context with current tab to control readiness checks
    updateActiveTab(activeTab);
  }, [activeTab, updateActiveTab]);
  const [sessionTerminatedModal, setSessionTerminatedModal] = useState({
    show: false,
    taskId: null
  });
  const [pagination, setPagination] = useState({
    currentPage: 1,
    pageSize: 10,
    totalItems: 0
  });
  const [filters, setFilters] = useState({
    search: '',
    type: null,
    status: null,
    created_at: null
  });

  // Check for session termination on component mount
  useEffect(() => {
    const sessionTerminated = sessionStorage.getItem("session_terminated");
    const terminatedTaskId = sessionStorage.getItem("session_terminated_task_id");
    
    if (sessionTerminated === "true" && terminatedTaskId) {
      setSessionTerminatedModal({
        show: true,
        taskId: terminatedTaskId
      });
      
      // Clear the flags from session storage
      sessionStorage.removeItem("session_terminated");
      sessionStorage.removeItem("session_terminated_task_id");
    }
  }, []);

  const handleCloseSessionTerminatedModal = useCallback(() => {
    setSessionTerminatedModal({
      show: false,
      taskId: null
    });
  }, []);

  const fetchTasks = useCallback(async (page = 1, pageSize = 10, filters = {}) => {
    try {
      setIsLoading(true);
      const skip = (page - 1) * pageSize;

      const queryParams = new URLSearchParams();
      if (filters.search) queryParams.append('search', filters.search);
      if (filters.type) queryParams.append('type', filters.type);
      if (filters.status) queryParams.append('status', filters.status);
      if (filters.created_at) {
        const formattedDate = dayjs(filters.created_at).format('YYYY-MM-DD');
        queryParams.append('created_at', formattedDate);
      }

      const tasks = await getPastCodeTasks(
        projectId,
        pageSize,
        skip,
        queryParams.toString()
      );

      const transformedData = transformSessionsResponse(tasks);
      setSessions(transformedData.sessions);
      setPagination(prev => ({
        ...prev,
        totalItems: transformedData.pagination.total_count
      }));
    } catch (error) {
      console.error('Error fetching tasks:', error);
    } finally {
      setIsLoading(false);
    }
  }, [projectId]);

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, currentPage: newPage }));
  };

  const handlePageSizeChange = (newSize) => {
    setPagination(prev => ({
      ...prev,
      pageSize: newSize,
      currentPage: 1
    }));
  };

  const handleFilterChange = useCallback((newFilters) => {
    setFilters(newFilters);
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  }, []);

  const handleHistoryOpen = () => {
    fetchTasks(pagination.currentPage, pagination.pageSize, filters);
    setIsHistoryOpen(true);
  };

  useEffect(() => {
    const historyParam = searchParams.get('history');
    if (historyParam === 'true' && !isHistoryOpen) {
      handleHistoryOpen();
    }
  }, [searchParams, isHistoryOpen]);

  useEffect(() => {
    if (!isHistoryOpen && searchParams.get('history') === 'true') {
      // Debounce URL cleanup to prevent rapid URL changes
      const timeoutId = setTimeout(() => {
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.delete('history');
        router.push(`${pathname}?${newSearchParams.toString()}`);
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [isHistoryOpen, searchParams, pathname, router]);

  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && tab !== activeTabRef.current) {
      setActiveTab(tab);
      activeTabRef.current = tab;
      // Remove the tab parameter from URL after setting the active tab
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.delete('tab');
      router.push(`${pathname}?${newSearchParams.toString()}`);
    }
  }, [searchParams, pathname, router]);

  useEffect(() => {
    // Debounce fetchTasks to prevent excessive API calls during rapid state changes
    const timeoutId = setTimeout(() => {
      fetchTasks(pagination.currentPage, pagination.pageSize, filters);
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [projectId, pagination.currentPage, pagination.pageSize, filters]);



  return (
    <div className="overflow-y-auto custom-scrollbar max-h-[78vh]">
      <div className="p-3">
        {/* Header Section with Tabs and Action Buttons */}
        <div className="flex items-center justify-between mb-3 h-8">
          {/* Left: Tabs */}
          <div className="bg-gray-100 h-8 flex border border-gray-200 rounded-md overflow-hidden">
            <BootstrapTooltip
              title={!is_having_permission() ? "You don't have permission" : "Start code generation"}
              placement="bottom"
            >
              <button
                onClick={() => {
                  if (is_having_permission() && activeTabRef.current !== 'generation') {
                    setActiveTab('generation');
                    activeTabRef.current = 'generation';

                    // Trigger readiness check when switching to generation tab
                    setTimeout(() => {
                      const event = new CustomEvent('switchToGeneration');
                      window.dispatchEvent(event);
                    }, 100);
                  }
                }}
                className={`px-4 py-1 typography-body-sm font-weight-medium transition-all duration-200 ease-in-out whitespace-nowrap ${
                  activeTab === 'generation'
                    ? 'bg-white text-primary-600 shadow-sm border-r border-gray-200'
                    : 'text-gray-600 hover:bg-primary-50 hover:text-primary-600'
                }`}
                disabled={!is_having_permission()}
              >
                Code Generation
              </button>
            </BootstrapTooltip>

            <BootstrapTooltip
              title={!is_having_permission() ? "You don't have permission" : "Start code maintenance"}
              placement="bottom"
            >
              <button
                onClick={() => {
                  if (is_having_permission() && activeTabRef.current !== 'maintenance') {
                    setActiveTab('maintenance');
                    activeTabRef.current = 'maintenance';
                  }
                }}
                className={`px-4 py-1 typography-body-sm font-weight-medium transition-all duration-200 ease-in-out whitespace-nowrap ${
                  activeTab === 'maintenance'
                    ? 'bg-white text-primary-600 shadow-sm border-l border-gray-200'
                    : 'text-gray-600 hover:bg-primary-50 hover:text-primary-600'
                }`}
                disabled={!is_having_permission()}
              >
                Code Maintenance
              </button>
            </BootstrapTooltip>
          </div>

          {/* Right: Action Buttons - Show different buttons based on active tab */}
          <div className="flex items-center gap-2 h-8">

            {/* Generation-specific buttons */}
            {activeTab === 'generation' && (
              <>
                {/* Select All / Deselect All Button - This will be controlled by generation page */}
                <button
                  id="generation-select-all-btn"
                  className="flex items-center px-3 py-1.5 bg-white border border-gray-300 rounded-md hover:bg-primary-50 hover:border-primary-300 typography-body-sm font-weight-medium transition-all duration-200 h-8"
                >
                  <div className="flex items-center justify-center mr-1.5">
                    <div className="w-3.5 h-3.5 flex items-center justify-center border border-gray-400 rounded transition-colors">
                    </div>
                  </div>
                  <span className="text-gray-700">Select All</span>
                </button>

                {/* History Button */}
                <BootstrapTooltip title={TOOLTIP_CONTENT.codeMaintenance.History} placement="bottom">
                  <button
                    id="generation-history-btn"
                    onClick={() => {
                      const newSearchParams = new URLSearchParams(searchParams);
                      newSearchParams.set("history", "true");
                      router.push(`${pathname}?${newSearchParams.toString()}`);
                    }}
                    className="flex items-center justify-center px-3 py-1.5 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-primary-50 hover:border-primary-300 hover:text-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 typography-body-sm font-weight-medium transition-all duration-200 h-8"
                  >
                    <RefreshCcw className="mr-1.5 w-3.5 h-3.5" />
                    <span>History</span>
                  </button>
                </BootstrapTooltip>

                {/* Refresh Button */}
                <BootstrapTooltip title="Refresh page" placement="bottom">
                  <button
                    onClick={() => window.location.reload()}
                    className="flex items-center justify-center px-3 py-1.5 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-primary-50 hover:border-primary-300 hover:text-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 typography-body-sm font-weight-medium transition-all duration-200 h-8"
                  >
                    <RefreshCw className="w-3.5 h-3.5" />
                    <span className="ml-1.5">Refresh</span>
                  </button>
                </BootstrapTooltip>

                {/* Start Session Button - This will be controlled by generation page */}
                <button
                  id="generation-start-session-btn"
                  className="flex items-center justify-center px-3 py-1.5 bg-primary hover:bg-primary-600 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 typography-body-sm font-weight-medium transition-all duration-200 h-8"
                >
                  <span>Start Session</span>
                </button>
              </>
            )}

            {/* Maintenance-specific buttons */}
            {activeTab === 'maintenance' && (
              <>
                {/* Deselect All / Select All Button - This will be controlled by maintenance page */}
                <button
                  id="maintenance-select-all-btn"
                  className="flex items-center px-3 py-1.5 bg-white border border-gray-300 rounded-md hover:bg-primary-50 hover:border-primary-300 typography-body-sm font-weight-medium transition-all duration-200 h-8"
                >
                  <div className="flex items-center justify-center mr-1.5">
                    <div className="w-3.5 h-3.5 flex items-center justify-center border border-gray-400 rounded transition-colors">
                    </div>
                  </div>
                  <span className="text-gray-700">Select All</span>
                </button>

                {/* History Button */}
                <BootstrapTooltip title={TOOLTIP_CONTENT.codeMaintenance.History} placement="bottom">
                  <button
                    onClick={() => {
                      const newSearchParams = new URLSearchParams(searchParams);
                      newSearchParams.set("history", "true");
                      router.push(`${pathname}?${newSearchParams.toString()}`);
                    }}
                    className="flex items-center justify-center px-3 py-1.5 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-primary-50 hover:border-primary-300 hover:text-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 typography-body-sm font-weight-medium transition-all duration-200 h-8"
                  >
                    <RefreshCcw className="mr-1.5 w-3.5 h-3.5" />
                    <span>History</span>
                  </button>
                </BootstrapTooltip>

                {/* Refresh Button */}
                <BootstrapTooltip title="Refresh repositories list" placement="bottom">
                  <button
                    onClick={() => {
                      // Trigger maintenance page refresh
                      const event = new CustomEvent('refreshMaintenance');
                      window.dispatchEvent(event);
                    }}
                    className="flex items-center justify-center px-3 py-1.5 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-primary-50 hover:border-primary-300 hover:text-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 typography-body-sm font-weight-medium transition-all duration-200 h-8"
                  >
                    <RefreshCw className="w-3.5 h-3.5" />
                    <span className="ml-1.5">Refresh</span>
                  </button>
                </BootstrapTooltip>

                {/* Start Session Button - This will be controlled by maintenance page */}
                <button
                  id="maintenance-start-session-btn"
                  className="flex items-center justify-center px-3 py-1.5 bg-primary hover:bg-primary-600 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 typography-body-sm font-weight-medium transition-all duration-200 h-8"
                >
                  <span>Start Session</span>
                </button>
              </>
            )}
          </div>
        </div>

        {/* Content based on active tab */}
        {activeTab === 'generation' && (
          <div>
            <ContainerList isModal={true} />
          </div>
        )}
        {activeTab === 'maintenance' && (
          <div>
            <MaintenancePage />
          </div>
        )}
      </div>

      {/* History Modal */}
      {isHistoryOpen && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center backdrop-blur-sm">
          <div className="bg-white rounded-lg w-[94vw] h-[94vh] flex flex-col shadow-xl">
            <div className="flex-1 overflow-hidden flex flex-col">
              <div className="flex-1 overflow-y-auto custom-scrollbar p-2">
                <Sessions
                  initialSessions={sessions}
                  isLoading={isLoading}
                  onFilterChange={handleFilterChange}
                  onCloseModal={() => setIsHistoryOpen(false)}
                  onRefresh={() => fetchTasks(pagination.currentPage, pagination.pageSize, filters)}
                  compactMode={true}
                />
              </div>
              <div className="sticky bottom-0 bg-white border-t border-gray-200 py-3 px-4">
                <div className="flex items-center justify-between">
                  <div className="typography-caption text-gray-500">
                    Showing {sessions.length} of {pagination.totalItems} sessions
                  </div>
                  <Pagination
                    currentPage={pagination.currentPage}
                    pageCount={Math.max(1, Math.ceil(pagination.totalItems / pagination.pageSize))}
                    pageSize={pagination.pageSize}
                    totalItems={pagination.totalItems}
                    onPageChange={handlePageChange}
                    onPageSizeChange={handlePageSizeChange}
                    pageSizeOptions={[5, 10, 20, 50]}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Session Terminated Modal */}
      {sessionTerminatedModal.show && (
        <div className="fixed inset-0 z-[60] bg-black bg-opacity-50 flex items-center justify-center backdrop-blur-sm">
          <div className="bg-white rounded-lg max-w-md w-full mx-4 shadow-xl">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0 w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    Session Terminated Due to Inactivity
                  </h3>
                </div>
              </div>
              
              <div className="mb-6">
                <p className="text-gray-600 mb-3">
                  Your previous coding session was automatically terminated due to inactivity to optimize resource usage.
                </p>
                <p className="text-gray-600 mb-3">
                  <strong>Good news:</strong> All your code changes have been safely pushed to the following branch:
                </p>
                <div className="bg-gray-50 rounded-md p-3 border">
                  <code className="text-sm font-mono text-gray-800">
                    cga-{sessionTerminatedModal.taskId}
                  </code>
                </div>
              </div>
              
              <div className="flex justify-end">
                <button
                  onClick={handleCloseSessionTerminatedModal}
                  className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {isVisible && <CodeGenerationModal />}
    </div>
  );
};

export default CodePage;